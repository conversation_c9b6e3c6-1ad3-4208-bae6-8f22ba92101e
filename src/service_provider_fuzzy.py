"""
Service provider fuzzy matching functionality.
"""
import ast
from typing import List, Dict, <PERSON><PERSON>
import re
from fuzzywuzzy import fuzz
from loguru import logger
import numpy as np
import pandas as pd
from gensim.models import Word2Vec
from tqdm import tqdm
from src.service_provider_matching.preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web

# Dictionary to link between OCR data and list of Service Provider columns
sp_dict = {
    "Name": "ServiceProviderName",
    "Address": "Address",
    # "streetname_name": "ServiceProviderName",
    # "suburb_name": "ServiceProviderName",
    "abn": "ABN",
    "email": "Email",
    "web": "HomePage",
    "phone_home": "PhoneNo_Home",
    "phone_work": "PhoneNo_Work",
    "phone_mobile": "PhoneNo_Mobile",
    "fax": "FaxNo",
}

# List of hyperparameters
top_n = 10  # Top number of fuzzy matches to keep for Name, Address
cos_l = 0.5  # Lower limit on cosine acceptance
A_log = 1.0  # Logarithmic amplitude for scaling ABN multi-matches
priority_dict = {  # Dictionary to link OCR fields to priority
    "Name": 1.0,
    "Address": 1.0,
    # "streetname_name": 0.5,
    # "suburb_name": 0.5,
    "abn": 1.0,
    "email": 1.0,
    "web": 0.25,
    "phone_home": 0.5,
    "phone_work": 0.5,
    "phone_mobile": 0.5,
    "fax": 0.5,
}

def validate_abn(nums: List[int]) -> bool:
    """
    Validate an ABN number
    
    Args:
        nums (List[int]): List of digits in the ABN
        
    Returns:
        bool: True if valid ABN, False otherwise
    """
    if len(nums) != 11:
        return False
    if not all(isinstance(x, int) for x in nums):
        return False
    if any(x > 9 for x in nums):
        return False
    if any(x < 0 for x in nums):
        return False

    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))
    return s % 89 == 0


def extract_abn(info: Dict) -> List[str]:
    """
    Extract ABN from document content
    
    Args:
        info (Dict): Document information
        
    Returns:
        List[str]: List of extracted ABNs
    """
    content = info.get("content", "")
    abns = []
    
    # Extract ABNs using regex patterns
    abn_extract_regex = r"(?:\d *){11}"
    abn_extract_regex1 = r"\d{2}-\d{3}-\d{3}-\d{3}"
    
    # Find all matches for the first pattern
    matches = re.findall(abn_extract_regex, content)
    for match in matches:
        # Remove spaces and check if it's a valid ABN
        digits = [int(d) for d in match if d.isdigit()]
        if validate_abn(digits):
            abns.append(''.join(str(d) for d in digits))
    
    # Find all matches for the second pattern
    matches = re.findall(abn_extract_regex1, content)
    for match in matches:
        # Remove hyphens and check if it's a valid ABN
        digits = [int(d) for d in match if d.isdigit()]
        if validate_abn(digits):
            abns.append(''.join(str(d) for d in digits))
    
    return abns

def extract_fax_number(content: str) -> List[str]:
    # Regex to capture Australian phone numbers including:
    # 1. Landlines like (02) 9876 5432
    # 2. Mobile numbers like 0412 345 678
    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.
    # Exclude international format like +61
    
    phone_number_regex = r"(\(0\d{1,2}\) \d{4} \d{4})|(\(04\d{2}\) \d{3} \d{3})|(\d{4} \d{3} \d{3})|13\d{4}|1300 \d{3} \d{3}|1800 \d{3} \d{3}"
    
    # Extract all matches
    matches = re.findall(phone_number_regex, content)
    
    # Flatten the list and remove any empty strings
    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]
    
    return flattened_matches


def extract_phone_number(content: str) -> List[str]:
    """
    Extract phone numbers from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted phone numbers
    """
    # Regex to capture Australian phone numbers including:
    # 1. Landlines like (02) 9876 5432
    # 2. Mobile numbers like 0412 345 678
    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.
    # Exclude international format like +61
    
    phone_number_regex = r"(\(0\d{1,2}\) \d{4} \d{4})|(\(04\d{2}\) \d{3} \d{3})|(\d{4} \d{3} \d{3})|13\d{4}|1300 \d{3} \d{3}|1800 \d{3} \d{3}"
    
    # Extract all matches
    matches = re.findall(phone_number_regex, content)
    
    # Flatten the list and remove any empty strings
    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]
    
    return flattened_matches


def extract_mobile_number(content: str) -> List[str]:
    """
    Extract mobile numbers from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted mobile numbers
    """
    # phone number extraction
    phone_number_regex = r"\+?61[- ]?\d{1,2}[- ]?\d{4}[- ]?\d{4}"
    matches = re.findall(phone_number_regex, content) 
    return matches


def extract_email(content: str) -> List[str]:
    """
    Extract email addresses from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted email addresses
    """
    # Replace newlines with spaces to normalize text
    content = content.replace("\n", " ")
    
    # Improved regex to capture emails after a colon or space
    pattern = r"(?<=[:\s])([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?!\S)"
    
    matches = re.findall(pattern, content)
    
    return matches


def extract_weblink(content: str) -> List[str]:
    """
    Extract web links from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted web links
    """
    weblink_regex = r"(http|ftp|https)://([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?"
    matches = re.findall(weblink_regex, content)
    return matches


def prepare_service_provider_info(info: Dict) -> Dict:
    """
    Prepare service provider information from extracted data
    
    Args:
        info (Dict): Dictionary containing extracted information
        
    Returns:
        Dict: Dictionary with prepared service provider information
    """
    service_provider_info = {}
    content = info.get("content", "")
    
    service_provider_info["phone_home"] = extract_phone_number(content)
    service_provider_info["phone_work"] = service_provider_info["phone_home"]
    service_provider_info["fax"] = service_provider_info["phone_home"]
    service_provider_info["phone_mobile"] = extract_mobile_number(content)
    service_provider_info["email"] = extract_email(content)
    service_provider_info["web"] = extract_weblink(content)
    service_provider_info["abn"] = extract_abn(info)
    service_provider_info["Name"] = info.get("service_provider", "")
    service_provider_info["Address"] = info.get("service_provider_address", "")
    service_provider_info["customer_postcode"] = info.get("service_provider", "")
    
    return service_provider_info


def fuzzy_match_service_provider(service_provider_info: Dict, serv_prov: pd.DataFrame) -> Dict:
    """
    Perform fuzzy matching on service provider information
    
    Args:
        service_provider_info (Dict): Dictionary with service provider information
        serv_prov (pd.DataFrame): DataFrame with service provider reference data
        
    Returns:
        Dict: Dictionary with matching results
    """
    # Prepare list of match index and cosine values
    match_index_list = []
    match_cosine_list = []
    field_list = list(sp_dict.keys())
    priority_scores = [priority_dict[field] for field in field_list]

    # Begin iterating over field list
    for field in field_list:
        if (service_provider_info.get(field, None) is not None or service_provider_info[field] is not np.NaN) and service_provider_info.get(field, ""):
            # fields stored in lists (numerical)
            if field in ["abn", "phone_home", "phone_work", "phone_mobile", "fax"]:
                query_list = [
                    preprocess_numbers(query)
                    for query in service_provider_info[field]
                ]  # Convert string to list of integers
                query_list = [query for query in query_list if query != 99999999]
                # print(query_list)
            # fields stored in lists (non-numerical)
            elif field in ["email", "web"]:
                query_list = [
                    preprocess_web(query) for query in service_provider_info[field]
                ]  # Convert string to list of strings
           
            elif field in ["Name", "Address"]:
                query_list = [
                    query for query in [service_provider_info[field]]
                ]  # multiple strings in list
                
                # Use fuzzywuzzy for fuzzy matching
                fuzzy_list = []
                for query in query_list:
                    # Perform fuzzy matching with each item in the field_list
                    for field_item in serv_prov[sp_dict[field]]:
                        similarity_score = fuzz.ratio(query.lower(), field_item.lower())
                        # print(query.lower(),field_item.lower(),similarity_score)
                        if similarity_score >= cos_l * 100:  # Check if the score exceeds the threshold
                            fuzzy_list.append((field_item, similarity_score))
                            # print(fuzzy_list)
                
                # Sort by similarity score in descending order
                fuzzy_list = sorted(fuzzy_list, key=lambda x: x[1], reverse=True)

                # Get the top N results based on similarity score
                query_fuzzy_list = [item[0] for item in fuzzy_list[:top_n]]
                cosine_dict = {
                    item[0]: item[1] / 100.0 for item in fuzzy_list[:top_n]
                }  # Prepare dictionary of cosine value mappings
                
                # Combine the original query with the fuzzy results
                query_list = query_list + query_fuzzy_list
                # print(query)
                # print(cosine_dict)
                # print(query_list)
      
            # fields not stored in lists
            else:
                query_list = [preprocess(service_provider_info[field])]  # Put string in list

    
            if field in ["abn"] and len(query_list) > 0:
                match_index = []
                match_query = []
                match_length = []
                for query in query_list:
                    match_check = serv_prov[sp_dict[field]].isin(query_list)
                    match_index += serv_prov[match_check].index.values.tolist()
                    match_query += serv_prov[match_check][sp_dict[field]].tolist()
                    match_length += [
                        len(match_index) for index in range(0, len(match_index))
                    ]  # Scaling of cosine score from number of matches returned
                    # print(match_query)
            else:
                match_check = serv_prov[sp_dict[field]].isin(query_list)
                match_index = serv_prov[match_check].index.values.tolist()
                match_query = serv_prov[match_check][sp_dict[field]].tolist()
                # print(match_check,match_index,match_query)
            
            # Map to cosine values
            if field in ["Name", "Address"]:
                match_dict = dict(
                    zip(match_index, [cosine_dict[item] for item in match_query])
                )
                match_cosine = [match_dict[idx] for idx in match_index]
            # elif field in ["abn"]:
            #     # print('abn')
            #     match_cosine = [
            #         1.0 / (A_log * np.log(match_length[idx]) + 1)
            #         for idx in range(0, len(match_index))
            #     ]
                # print(match_cosine)
            else:
                match_cosine = [1.0 for idx in range(0, len(match_index))]
            match_index_list.append(match_index)
            match_cosine_list.append(match_cosine)
        else:
            match_index_list.append([])
            match_cosine_list.append([])

    # Flatten entire match index list
    flat_index_list = [ind for sublist in match_index_list for ind in sublist]

    # Calculate counts, priority score and entities matched
    count_list = []
    for ind in set(flat_index_list):
        priority_score = 0
        fields_matched = []
        for n, match_index in enumerate(match_index_list):
            if ind in match_index:
                cosine_score = match_cosine_list[n][match_index.index(ind)]
                priority_score += priority_scores[n] * cosine_score
                fields_matched.append(field_list[n])
        count_list.append(
            (ind, flat_index_list.count(ind), priority_score, fields_matched)
        )

    # Keep Top 5 best matches in descending order of priority score
    sorted_count_list = sorted(count_list, key=lambda tup: (tup[2], len(tup[3])), reverse=True)[
        :5
    ]  # Sort by match count/ match priority?
    # print(sorted_count_list)
    # print(sorted_count_list)

    # Print best and second best matches to file

    # Initialize the second-best match variables
    best_match_count_2 = 0
    best_match_priority_2 = 0
    best_match_fields_2 = None
    best_match_list_2 = [None]

    if len(sorted_count_list) > 1:
        best_match_index = int([item[0] for item in sorted_count_list][0])
        best_match_count = int([item[1] for item in sorted_count_list][0])
        best_match_priority = [item[2] for item in sorted_count_list][0]
        best_match_fields = [item[3] for item in sorted_count_list][0]
        best_match_list = (
            serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
            .iloc[best_match_index]
            .values.tolist()
        )

        # Check for a tie in priority score and resolve by field match count
        best_match_index_2 = int([item[0] for item in sorted_count_list][1])
        best_match_fields_2 = [item[3] for item in sorted_count_list][1]
        best_match_priority_2 = [item[2] for item in sorted_count_list][1]
        best_match_list_2 = (
            serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
            .iloc[best_match_index_2]
            .values.tolist()
        )

        # If priority scores are the same, compare the number of fields matched
        if best_match_priority == best_match_priority_2:
            if len(best_match_fields) < len(best_match_fields_2):
                best_match_index_2 = int([item[0] for item in sorted_count_list][1])
                best_match_fields_2 = [item[3] for item in sorted_count_list][1]
                best_match_list_2 = (
                    serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
                    .iloc[best_match_index_2]
                    .values.tolist()
                )
            # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index_2].values.tolist()
    
    # Single Match
    elif len(sorted_count_list) == 1:
        best_match_index = int([item[0] for item in sorted_count_list][0])
        best_match_count = int([item[1] for item in sorted_count_list][0])
        best_match_priority = [item[2] for item in sorted_count_list][0]
        best_match_fields = [item[3] for item in sorted_count_list][0]
        best_match_list = (
            serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
            .iloc[best_match_index]
            .values.tolist()
        )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index].values.tolist()

        best_match_index_2 = None
        best_match_count_2 = 0
        best_match_priority_2 = 0
        best_match_fields_2 = None
        best_match_list_2 = [None]
    # No matches
    else:
        best_match_index = None
        best_match_count = 0
        best_match_priority = 0
        best_match_fields = None
        best_match_list = ["",""]

        best_match_index_2 = None
        best_match_count_2 = 0
        best_match_priority_2 = None
        best_match_fields_2 = None
        best_match_list_2 = [None]

    # export to list
    return {
        "best_match_list": best_match_list,
        "best_match_evidence": (best_match_fields, best_match_count, best_match_priority),
        "best_match_list2": best_match_list_2,
        "best_match_evidence2": (best_match_fields_2, best_match_count_2, best_match_priority_2),
        "list" : sorted_count_list
    }


def if_extra_gst_service_provider(info: Dict) -> bool:
    """
    Check if service provider requires extra GST processing
    
    Args:
        info (Dict): Document information
        
    Returns:
        bool: True if service provider requires extra GST, False otherwise
    """
    extra_gst_servie_provider_abn_set = {
        "***********",  # Pet Chemist Online
        "***********",  # Perth Animal Eye Hospital
        "***********",  # Melbourne Veterinary Specialist Centre-Essendon
        "***********",  # Hamilton Hill Veterinary Hospital
        "***********",  # Animal Eye Care
        "***********",  # Pets At Peace
        "***********",  # Dermatology for Animals
        "***********",  # Walk-In Clinic for Animals
    }
    abn = info.get("ABN", [])
    return len(extra_gst_servie_provider_abn_set & set(abn)) > 0


def find_similar_substring(content: str, target: str, max_diff: int = 2) -> List[str]:
    """
    Find a substring in the content that has the same length as the target substring,
    with only a few different characters or digits.

    Args:
        content (str): The string to search within.
        target (str): The substring to compare with.
        max_diff (int): Maximum number of allowed differences. Default is 2.

    Returns:
        List[str]: List of similar substrings found
    """
    similar_substrings = []
    
    # Implementation of fuzzy substring matching
    # This is a placeholder - implement the actual logic based on your requirements
    
    return similar_substrings


def load_service_provider_data(csv_path: str) -> pd.DataFrame:
    """
    Load and preprocess service provider data
    
    Args:
        csv_path (str): Path to the CSV file with service provider data
        
    Returns:
        pd.DataFrame: Preprocessed service provider DataFrame
    """
    # Read in Service Provider raw data
    serv_prov_raw = pd.read_csv(csv_path)
    serv_prov_raw = serv_prov_raw[serv_prov_raw['Is_Blocked']==0].reset_index(drop=True)
    serv_prov = serv_prov_raw.copy()  # create copy of raw data
    serv_prov = serv_prov.fillna("")
    serv_prov["PostCode"] = serv_prov["PostCode"].astype(str)
    
    # Preprocessing of Service Provider List
    serv_prov["Address"] = (
        serv_prov["Address"]
        + " "
        + serv_prov["City"]
        + " "
        + serv_prov["State"]
        + " "
        + serv_prov["PostCode"]
    )  # Concat fields to form full Address
    
    for field in [
        "ServiceProviderName",
        "Address",
        "ABN",
        "Email",
        "HomePage",
        "PhoneNo_Home",
        "PhoneNo_Work",
        "PhoneNo_Mobile",
        "FaxNo",
    ]:
        if field in ["ServiceProviderName", "Address"]:
            serv_prov[field] = serv_prov[field].apply(preprocess)
        elif field in ["Email", "HomePage"]:
            serv_prov[field] = serv_prov[field].apply(preprocess_web)
        elif field in [
            "PhoneNo_Home",
            "PhoneNo_Work",
            "PhoneNo_Mobile",
            "FaxNo",
            "PhoneNo_Home",
        ]:
            serv_prov[field] = serv_prov[field].apply(preprocess_numbers)
        elif field in ['ABN']:
            serv_prov[field] = serv_prov[field].apply(lambda x: int(x) if x != "" and pd.notnull(x) else x)
    
    return serv_prov


def process_service_provider_matching(document_intelligence_rule_res: Dict, csv_file_path: str) -> Dict:
    """
    Process service provider matching for all documents
    
    Args:
        document_intelligence_rule_res (Dict): Dictionary with document intelligence results
        csv_file_path (str): Path to the CSV file with service providers
        
    Returns:
        Dict: Updated document intelligence results with service provider matching
    """
    # Load service provider data
    serv_prov = load_service_provider_data(csv_file_path)
    logger.info("Service Provider data loaded and preprocessed")
    
    # Process each document
    for docfile, info_list in tqdm(document_intelligence_rule_res.items(), desc="Processing service provider matching"):
        for info in info_list:
            # Prepare service provider info
            service_provider_info = prepare_service_provider_info(info)
            
            # Perform fuzzy matching
            match_result = fuzzy_match_service_provider(service_provider_info, serv_prov)
            
            # Update the document with matching results
            info["service_provider_info"] = service_provider_info
            info["service_provider_info"].update(match_result)
            
            # Add convenience fields
            info["service_provider_fm"] = info["service_provider_info"]['best_match_list'][1]
            info["service_provider_no_fm"] = info["service_provider_info"]['best_match_list'][0]
            info["service_provider_evidence"] = info["service_provider_info"]['best_match_evidence']

            try:
                # Attempt to access the list elements
                best_match_list = info["service_provider_info"]['best_match_list2']
                info["service_provider_fm2"] = best_match_list[1]
                info["service_provider_no_fm2"] = best_match_list[0]
                info["service_provider_evidence2"] = info["service_provider_info"]['best_match_evidence2']
            except (IndexError, KeyError):
                # If the list is too short (IndexError) or the key doesn't exist (KeyError), set to None
                info["service_provider_fm2"] = None
                info["service_provider_no_fm2"] = None
                info["service_provider_evidence2"] = None

    return document_intelligence_rule_res


def main():
    """Example usage of the service provider matching functionality"""
    try:
        # Path to the service provider CSV file
        csv_file_path = "/workspaces/OCR_in_house/data/ref_service_provider_updated.csv"
        
        # Load service provider data
        serv_prov = load_service_provider_data(csv_file_path)
        print(f"Loaded {len(serv_prov)} service providers")
        
        # Example document info
        example_info = {
            "content": "JOHNSTON ST VET CLINIC\n123 Johnston Street\nFitzroy VIC 3065\nPhone: (03) 9417 4228\nABN: **************",
            "service_provider": "JOHNSTON ST VET CLINIC",
            "service_provider_address": "123 Johnston Street Fitzroy VIC 3065"
        }
        
        # Prepare service provider info
        service_provider_info = prepare_service_provider_info(example_info)
        print("Service Provider Info:")
        for key, value in service_provider_info.items():
            print(f"  {key}: {value}")
        
        # Perform fuzzy matching
        match_result = fuzzy_match_service_provider(service_provider_info, serv_prov)
        print("\nMatching Result:")
        print(f"  Best Match: {match_result['best_match_list']}")
        print(f"  Evidence: {match_result['best_match_evidence']}")
        print(f"  Second Best Match: {match_result['best_match_list2']}")

    except FileNotFoundError:
        print(
            "Error: service_providers.csv file not found. Please create the CSV file."
        )
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()