"""
Data loading functions for OCR processing.
"""
import pandas as pd
from loguru import logger
import os
from pathlib import Path
from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient
from datetime import datetime, timedelta

def load_claim_doc_path(df, engine):
    """
    Load document paths for claims from the database.
    """
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""
        SELECT
            c.ClaimNumber,
            d.*
        FROM [COS].[dbo].[Document] d
            LEFT JOIN [COS].[dbo].[Claim] c
            ON d.ClaimRefNumber = c.ClaimRefNumber
        WHERE [DocumentPath] LIKE '%csp%'
            AND [DocumentType] = 'ClaimInvoice'
            AND c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)
        return df_sot
    return pd.DataFrame()

def load_claim_truuth(df, engine):
    """
    Load claim truth data from the database.
    """
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""
        SELECT
            c.ClaimNumber,
            cc.*
        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc
            LEFT JOIN [COS].[dbo].[Claim] c
            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT
        WHERE c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_truuth = pd.read_sql(sql_query_policy, engine)
        return df_truuth
    return pd.DataFrame()

def load_upm_sot(df, engine):
    """
    Load UPM source of truth data from the database.
    """
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"]
            .astype(str)
            .apply(lambda x: x.split("-")[0])
            .unique()
            .tolist()
        )

        sql_query_policy = rf"""

        SELECT
            t.ClaimNo,
            t.InvoiceNo,
            s.ServiceProviderName,
            s.ServiceProviderNo,
            ci.DateInvoice,
            t.DateTreatment,
            t.TreatmentDrugRaw AS TreatmentDrugDescription,
            t.AmountInclVat,
            SUM(t.AmountInclVat) OVER (PARTITION BY t.InvoiceNo) AS InvoiceAmount

        FROM [PS-PRD-AZS-DWH1].[BIA].[model].[XML-ClaimTreatment] t
            LEFT JOIN [BIA].[model].[XML-ClaimPhysical] p
                ON t.ClaimNo = p.ClaimNo
            LEFT JOIN [BIA].[dbo].[Ref_VetServiceProvider] s
                ON p.ServiceProviderNo = s.ServiceProviderNo
            LEFT JOIN [BIA].[model].[XML-ClaimInvoice] ci
                ON t.InvoiceNo = ci.InvoiceNo AND t.ClaimNo = ci.ClaimNo

        WHERE t.ClaimNo
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_sot

def load_claim_truuth(df, engine):
    """
    Load Truuth data from the database.
    """
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""

        SELECT
            c.ClaimNumber,
            cc.CustomerId,
            cc.ClaimPolicyNumber,
            cc.CosReference,
            cc.OCRDocumentConfidence,
            cc.OCRResponseObject

        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc
            LEFT JOIN [COS].[dbo].[Claim] c
            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT
        WHERE c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_truuth = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_truuth


def download_blob_storage(
    doc_path_df: pd.DataFrame, 
    output_directory: str
):
    """
    Downloads files from Azure Blob Storage to a specified local directory.

    This function first checks if the output directory already exists.
    - If it does not exist, it creates the directory and proceeds to download
      all files specified in the doc_path_df DataFrame.
    - If it already exists, it skips the entire download process and prints a message.

    Args:
        blob_service_client (BlobServiceClient): An authenticated client object for 
                                                 accessing Azure Blob Storage.
        doc_path_df (pd.DataFrame): A DataFrame with columns 'DocContainer' and 'DocFile' 
                                    that specify the container and blob name for each file.
        output_directory (str): The path to the local folder where files will be saved.
    """
    # Use pathlib for robust path handling
    output_path = Path(output_directory)

    # Separate document container and document file
    doc_path_df['DocContainer'] = doc_path_df['DocumentPath'].str.split(pat="/", n=1, expand = True)[0]
    doc_path_df['DocFile'] = doc_path_df['DocumentPath'].str.split(pat="/", n=1, expand = True)[1]

    # Credentials
    source_key = os.getenv("BLOB_STORAGE_KEY")  # Ensure this environment variable is set
    source_account_name = os.getenv("BLOB_STORAGE_ACCOUNT_NAME")  # Ensure this environment variable is set

    sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,
                                        resource_types=ResourceTypes(
                                            service=True, container=True, object=True),
    permission=AccountSasPermissions(read=True),
                                        expiry=datetime.utcnow() + timedelta(hours=1))

    source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)


    # The main condition: only run if the output directory doesn't exist.
    if not output_path.exists():
        print(f"Directory '{output_path}' not found. Creating it and starting download...")
        
        # 1. Create the output directory
        os.makedirs(output_path)

        # 2. Define the helper function to download a single blob (nested)
        def _download_single_blob(blob_service_client: BlobServiceClient,container_name: str, file_name: str, dest_path: Path):
            """Downloads a single blob to a local file path."""
            try:
                container_client = blob_service_client.get_container_client(container_name)
                blob_client = container_client.get_blob_client(file_name)
                
                with open(dest_path, "wb") as f:
                    f.write(blob_client.download_blob().readall())
                # print(f"Successfully downloaded: {file_name}") # Optional: for verbose logging
            except Exception as e:
                print(f"Failed to download {file_name} from container {container_name}. Error: {e}")

        # 3. Iterate through the DataFrame and download each document
        print(f"Starting download of {len(doc_path_df)} files...")
        for idx in doc_path_df.index:
            container = doc_path_df.loc[idx, 'DocContainer']
            blob_name = doc_path_df.loc[idx, 'DocFile']
            
            # Construct the full destination path for the file
            destination_file_path = output_path / blob_name
            
            # Call the nested download function
            _download_single_blob(source_blob_service_client,container, blob_name, destination_file_path)
            
        print("All downloads completed.")
        
    else:
        # This block runs if the directory already exists.
        print(f"Directory '{output_path}' already exists. Skipping download.")


