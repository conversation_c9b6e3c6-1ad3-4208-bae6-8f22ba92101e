"""
Document processing functions for OCR pipeline.
"""
from typing import List, Dict, Tuple, Optional, Union, Any
from copy import deepcopy

from tqdm import tqdm

import string
import pandas as pd
import json

def is_numbers_and_punctuation(s):
    allowed_chars = set(string.digits + string.punctuation + " ")
    return all(char in allowed_chars for char in s)


def parse_document_intelligence_res(data_info: List[Dict] | Dict) -> Dict:
    if isinstance(data_info, Dict):
        data_info = [data_info]

    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)

    ans = {}
    for data in tqdm(data_info):
        file_path = data["file_path"]
        invoice = data["invoice"]
        content = invoice["content"]
        invoice_info = []
        for document in invoice["documents"]:
            service_provider = document["fields"].get("VendorName", {}).get("value", "") + " " + document["fields"].get("VendorAddressRecipient", {}).get("value", "")
            service_provider = service_provider.strip()
            service_provider_conf = 0.
            service_provider_count = int(document["fields"].get("VendorName", {}).get("value", "") != "") + int(document["fields"].get("VendorAddressRecipient", {}).get("value", "") != "")
            service_provider_conf = (document["fields"].get("VendorName", {}).get("confidence", 0.) or 0.) + (document["fields"].get("VendorAddressRecipient", {}).get("confidence", 0.) or 0.)
            if service_provider_conf > 0.:
                service_provider_conf /= service_provider_count
            # CHANGELOG: 04 NOV 2024 add service provider address extraction for service provider field fuzzy matching preparation
            service_provider_address = ""
            service_provider_address_value = document["fields"].get("VendorAddress", {}).get("value", {})
            service_provider_address_exist = service_provider_address_value.get("street_address", "") and (service_provider_address_value.get("postal_code", "") or service_provider_address_value.get("suburb", "") or service_provider_address_value.get("city", "")) 
            service_provider_address_content = document["fields"].get("VendorAddress", {}).get("content", "").replace("\t", " ").replace("\n", " ")
            if service_provider_address_exist:
                service_provider_address = service_provider_address_content

            customer_address_value = document["fields"].get("CustomerAddress", {}).get("value", {})
            customer_address_postcode = customer_address_value.get("postal_code", "")
            

            invoice_no = document["fields"].get("InvoiceId", {}).get("value", "")
            invoice_date = document["fields"].get("InvoiceDate", {}).get("value", "") or ""
            if not isinstance(invoice_date, str):
                invoice_date = invoice_date.isoformat()
            invoice_total_dict = document["fields"].get("InvoiceTotal", {}).get("value", {}) or {}
            invoice_total = invoice_total_dict.get("amount", -1)

            invoice_no_conf = document["fields"].get("InvoiceId", {}).get("confidence", 0.) or 0.
            invoice_date_conf = document["fields"].get("InvoiceDate", {}).get("confidence", 0.) or 0.
            invoice_total_conf = document["fields"].get("InvoiceTotal", {}).get("confidence", 0.) or 0.


            treatments = []
            cur_treatment_date = invoice_date
            cur_treatment_date_conf = invoice_date_conf

            for item in document["fields"].get("Items", {}).get("value", []):
                item_conf = item.get("confidence", 0.) or 0.

                treatment_date = item.get("value", {}).get("Date", {}).get("value", cur_treatment_date) or ""
                if not isinstance(treatment_date, str):
                    treatment_date = treatment_date.isoformat()
                treatment_date_conf = item.get("value", {}).get("Date", {}).get("confidence", cur_treatment_date_conf)
                if treatment_date_conf is None:
                    treatment_date_conf = item_conf

                if not treatment_date:
                    treatment_date = cur_treatment_date
                    treatment_date_conf = cur_treatment_date_conf
                cur_treatment_date = treatment_date
                cur_treatment_date_conf = treatment_date_conf

                desc = item.get("value", {}).get("Description", {}).get("content", "")
                product = item.get("value", {}).get("ProductCode", {}).get("content", "")
                # CHANGELOG: 04 NOV 2024 ignore product if it only contains numbers and puncs.
                if is_numbers_and_punctuation(product.strip()):
                     product = ""
                desc_conf = item.get("value", {}).get("Description", {}).get("confidence", item_conf) or item_conf
                product_conf = item.get("value", {}).get("ProductCode", {}).get("confidence", item_conf) or item_conf
                desc_conf = (desc_conf * int(desc!="") + product_conf * int(product!=""))/(int(desc!="") + int(product!="")+1e-7)
                desc = product + " " + desc
                desc = desc.strip()

                # CHANGELOG: 01 NOV 2024 default amount change from -1 to 0. This treatment line would be removed later during post process as amount  == 0
                amount_dict = item.get("value", {}).get("Amount", {}).get("value", {}) or {}
                amount = amount_dict.get("amount", 0)
                amount_conf = item.get("value", {}).get("Amount", {}).get("confidence", 0.)
                if amount_conf is None:
                    amount_conf = item_conf


                treatments.append({"treatment_date": treatment_date, 
                                   "treatment": desc, 
                                   "amount": amount, 
                                   "treatment_date_conf": treatment_date_conf,
                                   "treatment_conf": desc_conf, 
                                   "amount_conf": amount_conf, 
                                   "treatmentline_conf": item_conf})

            if not invoice_date:
                invoice_date = cur_treatment_date
                invoice_date_conf = cur_treatment_date_conf

            if not isinstance(invoice_date, str):
                invoice_date = invoice_date.isoformat()

            invoice_info.append(
                {
                    "service_provider": service_provider,
                    "service_provider_address": service_provider_address,
                    "customer_address_postcode": customer_address_postcode,
                    "content": content,
                    "invoice_no": invoice_no,
                    "invoice_date": invoice_date,
                    "invoice_total": invoice_total,
                    "service_provider_conf": service_provider_conf,
                    "invoice_no_conf": invoice_no_conf,
                    "invoice_date_conf": invoice_date_conf,
                    "invoice_total_conf": invoice_total_conf,
                    "treatments": treatments
                }
            )
            
        ans[file_path] = invoice_info


    return ans

def flatten_ocr_response(df: pd.DataFrame) -> pd.DataFrame:
    """
    Flatten the OCRResponseObject column into separate columns.
    Creates one row per treatment item, with multiple rows per original record if there are multiple invoices/treatments.
    
    Args:
        df: DataFrame containing 'OCRResponseObject', 'ClaimNumber', and 'OCRDocumentConfidence' columns
        
    Returns:
        DataFrame with flattened OCR response fields as separate columns
    """
    count = 0
    
    def extract_ocr_fields(ocr_obj: Any) -> List[Dict[str, Any]]:
        """Extract fields from a single OCR response object, returning a list of records for each treatment item."""
        
        # Initialize base fields that are common across all treatment items
        base_fields = {
            'ServiceProvider': None,
            'ServiceProviderNo': None,
            'ServiceProvider_conf': None,
            'TotalAmount': None,
            'TotalAmount_conf': None,
            'Truuth_Error': None
        }
        
        # Initialize treatment-specific fields
        treatment_fields_template = {
            'InvoiceDate': None,
            'InvoiceDate_conf': None,
            'InvoiceNumber': None,
            'InvoiceNumber_conf': None,
            'TreatmentDate': None,
            'TreatmentDate_conf': None,
            'TreatmentDescription': None,
            'TreatmentDescription_conf': None,
            'TreatmentAmount': None,
            'TreatmentAmount_conf': None,
        }
        
        try:
            # Convert to dict if it's a string
            if isinstance(ocr_obj, str):
                ocr_data = json.loads(ocr_obj)
            elif isinstance(ocr_obj, dict):
                ocr_data = ocr_obj
            else:
                # Return single record with all None values if can't parse
                return [{**base_fields, **treatment_fields_template}]
            
            # Navigate through the nested structure
            vet_xml_claim = ocr_data.get('VetXmlClaim', {})
            info_from_vet = vet_xml_claim.get('InfoFromVet', {})
            vet_info = info_from_vet.get('Vet', {})
            
            # Extract ServiceProvider fields (common across all records)
            base_fields['ServiceProvider'] = vet_info.get('PracticeName')
            base_fields['ServiceProviderNo'] = vet_info.get('PracticeId')
            base_fields['ServiceProvider_conf'] = float(vet_info.get('PracticeNameConfidence', 0))
            # Extract error information (common across all records)
            document_info = vet_xml_claim.get('DocumentInfo', {})
            error_codes = document_info.get('ErrorCode', [])
            if error_codes:
                # Combine all error descriptions
                error_descriptions = [error.get('Description', '') for error in error_codes if error.get('Description')]
                base_fields['Truuth_Error'] = '; '.join(error_descriptions) if error_descriptions else None
            
            # Extract invoice and treatment details from Conditions
            conditions = info_from_vet.get('Conditions', [])
            
            all_treatment_records = []
            
            if conditions:
                # Loop through all conditions
                for condition in conditions:
                    financial = condition.get('Financial', {})
                    
                    # Extract total amount (at condition level)
                    base_fields['TotalAmount'] = float(financial.get('TotalIncVat', 0))
                    base_fields['TotalAmount_conf'] = float(financial.get('TotalIncVatConfidence', 0))
                    
                    # Loop through all invoices
                    invoices = financial.get('Invoices', [])
                    for invoice in invoices:
                        invoice_number = invoice.get('InvoiceNumber')
                        invoice_number_conf = float(invoice.get('InvoiceNumberConfidence', 0))
                        invoice_date = invoice.get('Date')
                        invoice_date_conf = float(invoice.get('DateConfidence', 0))
                        
                        # Loop through all treatment items in this invoice
                        items = invoice.get('Items', [])
                        if items:
                            for item in items:
                                treatment_record = {
                                    **base_fields,
                                    'InvoiceNumber': invoice_number,
                                    'InvoiceNumber_conf': invoice_number_conf,
                                    'InvoiceDate': invoice_date,
                                    'InvoiceDate_conf': invoice_date_conf,
                                    'TreatmentDate': item.get('TreatmentDate'),
                                    'TreatmentDate_conf': float(item.get('TreatmentDateConfidence', 0)),
                                    'TreatmentDescription': item.get('Description'),
                                    'TreatmentDescription_conf': float(item.get('DescriptionConfidence', 0)),
                                    'TreatmentAmount': float(item.get('TotalIncVAT', 0)),
                                    'TreatmentAmount_conf': float(item.get('TotalIncVATConfidence', 0)),
                                }
                                all_treatment_records.append(treatment_record)
                        else:
                            # If no items, still create a record with invoice info
                            treatment_record = {
                                **base_fields,
                                **treatment_fields_template,
                                'InvoiceNumber': invoice_number,
                                'InvoiceNumber_conf': invoice_number_conf,
                                'InvoiceDate': invoice_date,
                                'InvoiceDate_conf': invoice_date_conf,
                            }
                            all_treatment_records.append(treatment_record)
            
            # If no records were created, return a single record with base fields
            if not all_treatment_records:
                all_treatment_records = [{**base_fields, **treatment_fields_template}]
            
            return all_treatment_records
            
        except (json.JSONDecodeError, KeyError, TypeError, IndexError) as e:
            # If any error occurs during parsing, return single record with None values
            print(count)
            print(f"Error parsing OCR response: {e}")
            return [{**base_fields, **treatment_fields_template}]
    count += 1
    # Process each row and collect all treatment records
    all_records = []
    
    for idx, row in df.iterrows():
        # Extract OCR fields (returns list of treatment records)
        treatment_records = extract_ocr_fields(row['OCRResponseObject'])
        
        # Add the original row data to each treatment record
        for treatment_record in treatment_records:
            combined_record = {
                'ClaimNumber': row['ClaimNumber'],
                'OCRDocumentConfidence': float(row['OCRDocumentConfidence']),
                **treatment_record
            }
            all_records.append(combined_record)
    
    # Create DataFrame from all records
    result_df = pd.DataFrame(all_records)
    
    # Define column order
    column_order = [
        'ClaimNumber',
        'OCRDocumentConfidence',
        'ServiceProvider',
        'ServiceProviderNo',
        'ServiceProvider_conf',
        'InvoiceDate',
        'InvoiceDate_conf',
        'InvoiceNumber',
        'InvoiceNumber_conf',
        'TreatmentDate',
        'TreatmentDate_conf',
        'TreatmentDescription',
        'TreatmentDescription_conf',
        'TreatmentAmount',
        'TreatmentAmount_conf',
        'TotalAmount',
        'TotalAmount_conf',
        'Truuth_Error'
    ]
    
    return result_df[column_order]