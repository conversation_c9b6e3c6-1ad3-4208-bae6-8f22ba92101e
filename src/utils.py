"""
Utility functions for OCR processing.
"""
from typing import List
import <PERSON><PERSON><PERSON>ein

def find_similar_substring(text: str, substring: str, max_diff: int = 2) -> List[str]:
    """
    Find substrings in text that are similar to the given substring.
    
    Args:
        text: The text to search in
        substring: The substring to search for
        max_diff: Maximum Levenshtein distance allowed
        
    Returns:
        List of similar substrings found
    """
    if not text or not substring:
        return []
    
    words = text.split()
    similar_substrings = []
    
    for i in range(len(words)):
        for j in range(i, min(i + 5, len(words))):
            candidate = " ".join(words[i:j+1])
            distance = Levenshtein.distance(candidate.lower(), substring.lower())
            if distance <= max_diff:
                similar_substrings.append(candidate)
    
    return similar_substrings

def setup_logging():
    """Configure logging for the application."""
    from loguru import logger
    import sys
    
    # Remove default handler
    logger.remove()
    
    # Add a handler for stdout with a specific format
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Add a file handler
    logger.add(
        "logs/ocr_pipeline.log",
        rotation="10 MB",
        retention="1 week",
        level="DEBUG"
    )