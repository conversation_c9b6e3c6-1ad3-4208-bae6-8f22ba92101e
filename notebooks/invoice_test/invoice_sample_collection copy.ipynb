{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "from src.sample_collection.sql_engine import *\n", "from pathlib import Path\n", "from dotenv import load_dotenv\n", "\n", "# Adjust the path to properly include the src directory\n", "module_path = os.path.abspath(os.path.join(\"..\", \"..\"))  # Go up two levels to reach project root\n", "if module_path not in sys.path:\n", "    sys.path.append(module_path)\n", "\n", "# Explicitly load the .env file\n", "load_dotenv()\n", "ROOTDIR = Path(\"/workspaces/OCR_in_house/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sample Colection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SQL Engine Connection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Engine disconnected successfully\n"]}], "source": ["# engine = Engine.disconnect()\n", "engine = Engine.get_engine(server=\"10.3.0.90\", database=\"BIA\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read Selected Sample Invoices from Spreadsheet"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["samples_di = pd.read_csv(ROOTDIR / 'data/1_samples_DI.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve and download document path"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["def load_claim_doc_path(df, engine):\n", "    if not df.empty:\n", "        claim_numbers = \"','\".join(\n", "            df[\"ClaimNo\"].astype(str).apply(lambda x: x.split(\"-\")[0]).unique().tolist()\n", "        )\n", "\n", "        sql_query_policy = rf\"\"\"\n", "\n", "        SELECT\n", "            <PERSON><PERSON><PERSON><PERSON>,\n", "            d.*\n", "        FROM [COS].[dbo].[Document] d\n", "            LEFT JOIN [COS].[dbo].[Claim] c\n", "            ON d.ClaimRefNumber = c.ClaimRefNumber\n", "        WHERE [DocumentPath] LIKE '%csp%'\n", "            AND [DocumentType] = 'ClaimInvoice'\n", "            AND c<PERSON><PERSON>\n", "                IN ('{claim_numbers}')\n", "        \"\"\"\n", "        df_sot = pd.read_sql(sql_query_policy, engine)\n", "\n", "    # Return both DataFrames\n", "    return df_sot"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["doc_path = load_claim_doc_path(samples_di, engine)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Separate document container and document file\n", "doc_path['DocContainer'] = doc_path['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "doc_path['DocFile'] = doc_path['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]\n", "\n", "# define credentials\n", "    \n", "source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='\n", "\n", "source_account_name = 'p3storageprod'  #'p3storagearchive'\n", "\n", "sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,\n", "                                     resource_types=ResourceTypes(\n", "                                         service=True, container=True, object=True),\n", "permission=AccountSasPermissions(read=True),\n", "                                     expiry=datetime.utcnow() + <PERSON><PERSON><PERSON>(hours=1))\n", "\n", "source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)\n", "\n", "# Create download function\n", "def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "# Download documents from blob storage to local folder called pdf             \n", "for idx in doc_path.index:\n", "    path =  ROOTDIR / 'data/samples/1_samples_DI' / doc_path['DocFile'][idx]\n", "    download(source_blob_service_client,doc_path['DocContainer'].iloc[idx],doc_path['DocFile'].iloc[idx],path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve Truth Output"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["def load_claim_truuth(df, engine):\n", "    if not df.empty:\n", "        claim_numbers = \"','\".join(\n", "            df[\"ClaimNo\"].astype(str).apply(lambda x: x.split(\"-\")[0]).unique().tolist()\n", "        )\n", "\n", "        sql_query_policy = rf\"\"\"\n", "\n", "        SELECT\n", "            <PERSON><PERSON><PERSON><PERSON>,\n", "            cc.*\n", "        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc\n", "            LEFT JOIN [COS].[dbo].[Claim] c\n", "            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT\n", "        WHER<PERSON> c<PERSON>\n", "                IN ('{claim_numbers}')\n", "        \"\"\"\n", "        df_truuth = pd.read_sql(sql_query_policy, engine)\n", "\n", "    # Return both DataFrames\n", "    return df_truuth"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["df_truuth = load_claim_truuth(samples_di, engine)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve Source of Truth UPM database"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["def load_upm_sot(df, engine):\n", "    if not df.empty:\n", "        claim_numbers = \"','\".join(\n", "            df[\"ClaimNo\"]\n", "            .astype(str)\n", "            .apply(lambda x: x.split(\"-\")[0])\n", "            .unique()\n", "            .tolist()\n", "        )\n", "\n", "        sql_query_policy = rf\"\"\"\n", "\n", "        SELECT\n", "            <PERSON><PERSON>,\n", "            l.InvoiceNo,\n", "            <PERSON><PERSON>,\n", "            ci.DateInvoice,\n", "            bcs.DateCreated,\n", "            t.<PERSON>,\n", "            t.Date<PERSON>,\n", "            <PERSON>.<PERSON>,\n", "            t.<PERSON>nclVat,\n", "            SUM(t.AmountInclVat) OVER (PARTITION BY l.InvoiceNo) AS InvoiceAmount\n", "\n", "        FROM [PS-PRD-AZS-DWH1].[BIA].[dbo].[BaseData_ClaimTreatment] t\n", "\n", "            LEFT JOIN [PS-PRD-AZS-DWH1].[BIA].[dbo].[Ref_TreatmentDrug] r\n", "                ON t.Treatment<PERSON>rug<PERSON>ode = r.<PERSON>\n", "            LEFT JOIN [BIA].[dbo].[Ref_VetServiceProvider] s\n", "                ON t.ServiceProviderNo = s.ServiceProvider<PERSON>o\n", "            LEFT JOIN [BIA].[model].[XML-ClaimInvoice-link] l\n", "                ON l.ClaimNo= t.<PERSON>laim<PERSON>o and l.InvoiceLineNo=t.InvoiceLineNo\n", "            LEFT JOIN [BIA].[model].[XML-ClaimInvoice] ci\n", "                ON l.InvoiceNo = ci.InvoiceNo AND l.ClaimNo = ci.ClaimNo\n", "            LEFT JOIN [BIA].[dbo].BaseData_Claim_Summary bcs\n", "                ON t.ClaimNo = bcs.ClaimNo\n", "                AND t.InvoiceLineNo = bcs.InvoiceLineNo\n", "        WHERE t.C<PERSON>o\n", "                IN ('{claim_numbers}')\n", "        \"\"\"\n", "        df_sot = pd.read_sql(sql_query_policy, engine)\n", "\n", "    # Return both DataFrames\n", "    return df_sot"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["df_sot = load_upm_sot(samples_di, engine)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}