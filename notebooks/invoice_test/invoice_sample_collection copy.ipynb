import pandas as pd
from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient
from datetime import datetime, timedelta

%load_ext autoreload
%autoreload 2

import pandas as pd
import os
from pathlib import Path
from typing import Union, List, Optional
import logging

def merge_files_from_folder(
    folder_path: Union[str, Path],
    output_file: Optional[str] = None,
    merge_type: str = 'outer',
    file_types: List[str] = ['csv', 'xlsx', 'xls'],
    encoding: str = 'utf-8'
) -> pd.DataFrame:
    """
    Merge CSV and Excel files from a folder based on column names.
    
    Parameters:
    -----------
    folder_path : str or Path
        Path to the folder containing files to merge
    output_file : str, optional
        Path to save the merged file. If None, only returns DataFrame
    merge_type : str, default 'outer'
        Type of merge to perform ('outer', 'inner', 'left', 'right')
    file_types : list, default ['csv', 'xlsx', 'xls']
        File extensions to include in the merge
    encoding : str, default 'utf-8'
        Encoding for CSV files
    
    Returns:
    --------
    pd.DataFrame
        Merged DataFrame containing all data
    """
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Convert to Path object
    folder_path = Path(folder_path)
    
    if not folder_path.exists():
        raise FileNotFoundError(f"Folder not found: {folder_path}")
    
    # Find all files with specified extensions
    files_to_merge = []
    for ext in file_types:
        files_to_merge.extend(folder_path.glob(f"*.{ext}"))
    
    if not files_to_merge:
        raise ValueError(f"No files found with extensions {file_types} in {folder_path}")
    
    logger.info(f"Found {len(files_to_merge)} files to merge")
    
    dataframes = []
    
    # Read each file
    for file_path in files_to_merge:
        try:
            logger.info(f"Processing: {file_path.name}")
            
            # Read file based on extension
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding=encoding)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                continue
            
            # Add source file column for tracking
            df['source_file'] = file_path.name
            
            # Clean column names (strip whitespace, make consistent)
            df.columns = df.columns.str.strip()
            
            # Rename 'Comment' to 'Comments' if present
            if 'Comment' in df.columns:
                df = df.rename(columns={'Comment': 'Comments'})
                logger.info(f"Renamed 'Comment' to 'Comments' in {file_path.name}")
            
            dataframes.append(df)
            logger.info(f"Loaded {len(df)} rows from {file_path.name}")
            
        except Exception as e:
            logger.error(f"Error processing {file_path.name}: {str(e)}")
            continue
    
    if not dataframes:
        raise ValueError("No files could be successfully processed")
    
    # Start with the first dataframe
    merged_df = dataframes[0]
    
    # Merge with subsequent dataframes
    for i, df in enumerate(dataframes[1:], 1):
        try:
            # Find common columns (excluding source_file)
            common_cols = list(set(merged_df.columns) & set(df.columns))
            common_cols = [col for col in common_cols if col != 'source_file']
            
            if common_cols:
                logger.info(f"Merging on common columns: {common_cols}")
                # Merge on all common columns
                merged_df = pd.merge(
                    merged_df, 
                    df, 
                    on=common_cols, 
                    how=merge_type,
                    suffixes=('', f'_file{i+1}')
                )
            else:
                logger.info("No common columns found, concatenating vertically")
                # If no common columns, concatenate vertically
                merged_df = pd.concat([merged_df, df], ignore_index=True, sort=False)
                
        except Exception as e:
            logger.error(f"Error merging dataframe {i+1}: {str(e)}")
            continue
    
    logger.info(f"Final merged dataframe shape: {merged_df.shape}")
    
    # Save to file if output path provided
    if output_file:
        output_path = Path(output_file)
        
        if output_path.suffix.lower() == '.csv':
            merged_df.to_csv(output_path, index=False, encoding=encoding)
        elif output_path.suffix.lower() in ['.xlsx', '.xls']:
            merged_df.to_excel(output_path, index=False)
        else:
            # Default to CSV
            merged_df.to_csv(output_path.with_suffix('.csv'), index=False, encoding=encoding)
        
        logger.info(f"Merged file saved to: {output_path}")
    
    return merged_df


def merge_files_simple_concat(
    folder_path: Union[str, Path],
    output_file: Optional[str] = None,
    file_types: List[str] = ['csv', 'xlsx', 'xls'],
    encoding: str = 'utf-8'
) -> pd.DataFrame:
    """
    Simple concatenation of files (stacking them vertically).
    Use this when files have the same structure.
    
    Parameters:
    -----------
    folder_path : str or Path
        Path to the folder containing files
    output_file : str, optional
        Path to save the merged file
    file_types : list, default ['csv', 'xlsx', 'xls']
        File extensions to include
    encoding : str, default 'utf-8'
        Encoding for CSV files
    
    Returns:
    --------
    pd.DataFrame
        Concatenated DataFrame
    """
    
    folder_path = Path(folder_path)
    
    if not folder_path.exists():
        raise FileNotFoundError(f"Folder not found: {folder_path}")
    
    # Find all files
    files_to_merge = []
    for ext in file_types:
        files_to_merge.extend(folder_path.glob(f"*.{ext}"))
    
    if not files_to_merge:
        raise ValueError(f"No files found with extensions {file_types}")
    
    dataframes = []
    
    for file_path in files_to_merge:
        try:
            # Read file
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding=encoding)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                continue
            
            # Add source file info
            df['source_file'] = file_path.name
            
            # Clean column names
            df.columns = df.columns.str.strip()
            
            # Rename 'Comment' to 'Comments' if present
            if 'Comment' in df.columns:
                df = df.rename(columns={'Comment': 'Comments'})
            
            dataframes.append(df)
            
        except Exception as e:
            print(f"Error processing {file_path.name}: {str(e)}")
            continue
    
    if not dataframes:
        raise ValueError("No files could be successfully processed")
    
    # Concatenate all dataframes
    merged_df = pd.concat(dataframes, ignore_index=True, sort=False)
    
    # Save if output file specified
    if output_file:
        output_path = Path(output_file)
        
        if output_path.suffix.lower() == '.csv':
            merged_df.to_csv(output_path, index=False, encoding=encoding)
        elif output_path.suffix.lower() in ['.xlsx', '.xls']:
            merged_df.to_excel(output_path, index=False)
        else:
            merged_df.to_csv(output_path.with_suffix('.csv'), index=False, encoding=encoding)
    
    return merged_df

# Example usage

try:
    result = merge_files_from_folder(
        folder_path="./data_files",
        output_file="merged_data.csv",
        merge_type="outer"
    )
    print(f"Successfully merged {len(result)} rows")
    print(f"Columns: {list(result.columns)}")
except Exception as e:
    print(f"Error: {e}")

# Example 2: Simple concatenation
try:
    result = merge_files_simple_concat(
        folder_path='/home/<USER>/repos/OCR_in_house/data/input/ocr_audit',
        output_file='/home/<USER>/repos/OCR_in_house/data/input/combined_audit_data.xlsx'
    )
    print(f"Successfully concatenated {len(result)} rows")
except Exception as e:
    print(f"Error: {e}")

df = merge_files_in_folder('/home/<USER>/repos/OCR_in_house/data/input', '/home/<USER>/repos/OCR_in_house/data/input/combined_audit_data.xlsx')

df = pd.read_csv("/home/<USER>/repos/OCR_in_house/data/audit_doc_path.csv")

import os
import pandas as pd
from azure.identity import ClientSecretCredential
from azure.storage.blob import BlobClient

def download_blobs_from_df(df: pd.DataFrame, tenant_id: str, client_id: str, client_secret: str,
                           storage_account_name: str, download_dir: str):
    """
    Download blobs listed in a DataFrame from Azure Blob Storage using ClientSecretCredential.

    Parameters:
    - df: pandas DataFrame with columns 'DocContainer' and 'DocFile'
    - tenant_id, client_id, client_secret: for Azure authentication
    - storage_account_name: your Azure Storage account name (e.g., "mystorageaccount")
    - download_dir: local directory to save the downloaded files
    """
    
    # Authenticate
    credential = ClientSecretCredential(tenant_id, client_id, client_secret)
    
    # Ensure download directory exists
    os.makedirs(download_dir, exist_ok=True)

    df['DocContainer'] = df['DocumentPath'].str.split(pat="/", n=1, expand = True)[0]
    df['DocFile'] = df['DocumentPath'].str.split(pat="/", n=1, expand = True)[1]


    # Loop through each row in the DataFrame
    for index, row in df.iterrows():
        container = row['DocContainer']
        blob_name = row['DocFile']
        
        # Create BlobClient
        blob_url = f"https://{storage_account_name}.blob.core.windows.net"
        blob_client = BlobClient(account_url=blob_url,
                                 container_name=container,
                                 blob_name=blob_name,
                                 credential=credential)
        
        # Local file path
        local_path = os.path.join(download_dir, os.path.basename(blob_name))
        
        try:
            print(f"Downloading: {blob_name} from container: {container}")
            with open(local_path, "wb") as file:
                data = blob_client.download_blob()
                file.write(data.readall())
            print(f"✅ Downloaded to: {local_path}")
        except Exception as e:
            print(f"❌ Failed to download {blob_name}: {e}")


# Your credentials (DO NOT hardcode in production)
tenant_id = "ce9f1d01-56a1-4c06-bf68-2cfbf51ff729"
client_id = "6bd29fad-4f0a-4058-9bef-5c46346c0929"
client_secret = "****************************************"
storage_account_name = 'p3storagearchive'
download_dir = '/home/<USER>/repos/OCR_in_house/data/samples/audit_samples_archive'

# Run the download
download_blobs_from_df(df, tenant_id, client_id, client_secret, storage_account_name, download_dir)


df

import sys
import os
from src.sample_collection.sql_engine import *
from pathlib import Path
from dotenv import load_dotenv

# Adjust the path to properly include the src directory
module_path = os.path.abspath(os.path.join("..", ".."))  # Go up two levels to reach project root
if module_path not in sys.path:
    sys.path.append(module_path)

# Explicitly load the .env file
load_dotenv()
ROOTDIR = Path("/workspaces/OCR_in_house/")

# engine = Engine.disconnect()
engine = Engine.get_engine(server="10.3.0.90", database="BIA")

samples_di = pd.read_csv(ROOTDIR / 'data/1_samples_DI.csv')

def load_claim_doc_path(df, engine):
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""

        SELECT
            c.ClaimNumber,
            d.*
        FROM [COS].[dbo].[Document] d
            LEFT JOIN [COS].[dbo].[Claim] c
            ON d.ClaimRefNumber = c.ClaimRefNumber
        WHERE [DocumentPath] LIKE '%csp%'
            AND [DocumentType] = 'ClaimInvoice'
            AND c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_sot

doc_path = load_claim_doc_path(samples_di, engine)

# Separate document container and document file
doc_path['DocContainer'] = doc_path['DocumentPath'].str.split(pat="/", n=1, expand = True)[0]
doc_path['DocFile'] = doc_path['DocumentPath'].str.split(pat="/", n=1, expand = True)[1]

# define credentials
    
source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='

source_account_name = 'p3storageprod'  #'p3storagearchive'

sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,
                                     resource_types=ResourceTypes(
                                         service=True, container=True, object=True),
permission=AccountSasPermissions(read=True),
                                     expiry=datetime.utcnow() + timedelta(hours=1))

source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)

# Create download function
def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):
        container_client = blob_service_client.get_container_client(container_name)
        blob_client = container_client.get_blob_client(file_name)
        with open(dest_path, "wb") as f:
            f.write(blob_client.download_blob().readall())

# Download documents from blob storage to local folder called pdf             
for idx in doc_path.index:
    path =  ROOTDIR / 'data/samples/1_samples_DI' / doc_path['DocFile'][idx]
    download(source_blob_service_client,doc_path['DocContainer'].iloc[idx],doc_path['DocFile'].iloc[idx],path)

def load_claim_truuth(df, engine):
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""

        SELECT
            c.ClaimNumber,
            cc.*
        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc
            LEFT JOIN [COS].[dbo].[Claim] c
            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT
        WHERE c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_truuth = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_truuth

df_truuth = load_claim_truuth(samples_di, engine)

def load_upm_sot(df, engine):
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"]
            .astype(str)
            .apply(lambda x: x.split("-")[0])
            .unique()
            .tolist()
        )

        sql_query_policy = rf"""

        SELECT
            t.ClaimNo,
            l.InvoiceNo,
            s.ServiceProviderName,
            ci.DateInvoice,
            bcs.DateCreated,
            t.TreatmentDrugCode,
            t.DateTreatment,
            r.TreatmentDrugDescription,
            t.AmountInclVat,
            SUM(t.AmountInclVat) OVER (PARTITION BY l.InvoiceNo) AS InvoiceAmount

        FROM [PS-PRD-AZS-DWH1].[BIA].[dbo].[BaseData_ClaimTreatment] t

            LEFT JOIN [PS-PRD-AZS-DWH1].[BIA].[dbo].[Ref_TreatmentDrug] r
                ON t.TreatmentDrugCode = r.TreatmentDrugCode
            LEFT JOIN [BIA].[dbo].[Ref_VetServiceProvider] s
                ON t.ServiceProviderNo = s.ServiceProviderNo
            LEFT JOIN [BIA].[model].[XML-ClaimInvoice-link] l
                ON l.ClaimNo= t.ClaimNo and l.InvoiceLineNo=t.InvoiceLineNo
            LEFT JOIN [BIA].[model].[XML-ClaimInvoice] ci
                ON l.InvoiceNo = ci.InvoiceNo AND l.ClaimNo = ci.ClaimNo
            LEFT JOIN [BIA].[dbo].BaseData_Claim_Summary bcs
                ON t.ClaimNo = bcs.ClaimNo
                AND t.InvoiceLineNo = bcs.InvoiceLineNo
        WHERE t.ClaimNo
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_sot

df_sot = load_upm_sot(samples_di, engine)